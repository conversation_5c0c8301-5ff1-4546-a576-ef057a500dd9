# 工单数据库表结构文档

本文档记录了工单系统相关的数据库表结构信息。

## 表列表

以下是工单系统相关的数据库表：

1. `w_order` - 工单主表
2. `w_order_operation_record` - 工单操作记录表
3. `w_order_template` - 工单模板表
4. `w_order_transition_rule` - 工单状态转换规则表
5. `w_order_workflow_instance` - 工单工作流实例表
6. `w_order_workflow_node` - 工单工作流节点表
7. `w_order_workflow_node_history` - 工单工作流节点历史表
8. `w_order_workflow_node_rollback` - 工单工作流节点回滚表
9. `w_order_workflow_template` - 工单工作流模板表
10. `w_order_workflow_transition` - 工单工作流转换表

---

## 1. w_order - 工单主表

工单系统的核心表，存储工单的基本信息。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| order_no | varchar(50) | NO | UNI | NULL | 工单编号 |
| type | int | NO | MUL | NULL | 工单类型 |
| title | varchar(200) | NO |  | NULL | 工单标题 |
| description | text | YES |  | NULL | 工单描述 |
| status | int | NO | MUL | NULL | 工单状态 |
| priority | int | NO |  | 3 | 优先级 |
| source_type | int | YES |  | NULL | 来源类型 |
| source_id | varchar(100) | YES |  | NULL | 来源ID |
| customer_id | bigint | YES | MUL | NULL | 客户ID |
| customer_name | varchar(100) | YES |  | NULL | 客户姓名 |
| customer_phone | varchar(20) | YES |  | NULL | 客户电话 |
| customer_address | varchar(500) | YES |  | NULL | 客户地址 |
| car_brand | varchar(50) | YES |  | NULL | 车辆品牌 |
| car_model | varchar(100) | YES |  | NULL | 车辆型号 |
| car_vin | varchar(50) | YES |  | NULL | 车架号 |
| charger_code | varchar(50) | YES |  | NULL | 充电桩编码 |
| charger_model | varchar(100) | YES |  | NULL | 充电桩型号 |
| install_count | int | YES |  | NULL | 安装数量 |
| creator_id | bigint | YES | MUL | NULL | 创建人ID |
| assignee_id | bigint | YES | MUL | NULL | 指派人ID |
| project_manager_id | bigint | YES | MUL | NULL | 项目经理ID |
| station_id | bigint | YES | MUL | NULL | 站点ID |
| service_person_id | bigint | YES | MUL | NULL | 服务人员ID |
| template_id | bigint | YES | MUL | NULL | 模板ID |
| survey_appointment_time | datetime | YES |  | NULL | 勘察预约时间 |
| install_appointment_time | datetime | YES |  | NULL | 安装预约时间 |
| finish_time | datetime | YES |  | NULL | 完成时间 |
| settlement_amount | decimal(10,2) | YES |  | NULL | 结算金额 |
| settlement_tax | decimal(10,2) | YES |  | NULL | 结算税费 |
| settlement_total | decimal(10,2) | YES |  | NULL | 结算总额 |
| remark | text | YES |  | NULL | 备注 |
| ext_data | text | YES |  | NULL | 扩展数据 |
| version | int | NO |  | 0 | 版本号 |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 2. w_order_operation_record - 工单操作记录表

记录工单的所有操作历史。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| w_order_id | bigint | NO | MUL | NULL | 工单ID |
| w_order_no | varchar(50) | NO | MUL | NULL | 工单编号 |
| operator_id | bigint | YES | MUL | NULL | 操作人ID |
| operator_name | varchar(100) | YES |  | NULL | 操作人姓名 |
| operation_type | int | NO | MUL | NULL | 操作类型 |
| operation_desc | varchar(500) | YES |  | NULL | 操作描述 |
| affected_user_id | bigint | YES | MUL | NULL | 受影响用户ID |
| affected_user_name | varchar(100) | YES |  | NULL | 受影响用户姓名 |
| before_status | int | YES |  | NULL | 操作前状态 |
| after_status | int | YES |  | NULL | 操作后状态 |
| remark | text | YES |  | NULL | 备注 |
| operation_data | text | YES |  | NULL | 操作数据 |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 3. w_order_template - 工单模板表

定义不同类型工单的模板配置。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| template_name | varchar(100) | NO |  | NULL | 模板名称 |
| w_order_type | int | NO | MUL | NULL | 工单类型 |
| brand_id | bigint | YES | MUL | NULL | 品牌ID |
| enabled | tinyint(1) | NO |  | 1 | 是否启用 |
| auto_assign | tinyint(1) | NO |  | 0 | 自动分配 |
| batch_import | tinyint(1) | NO |  | 0 | 批量导入 |
| service_type | int | YES |  | NULL | 服务类型 |
| settlement_type | int | YES |  | NULL | 结算类型 |
| require_signature | tinyint(1) | NO |  | 0 | 需要签名 |
| company_pledge_percentage | decimal(5,2) | YES |  | NULL | 公司质保比例 |
| station_pledge_percentage | decimal(5,2) | YES |  | NULL | 站点质保比例 |
| description | text | YES |  | NULL | 描述 |
| template_config | text | YES |  | NULL | 模板配置 |
| sort | int | NO |  | 0 | 排序 |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 4. w_order_transition_rule - 工单状态转换规则表

定义工单状态转换的规则和条件。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| rule_name | varchar(100) | NO |  | NULL | 规则名称 |
| description | text | YES |  | NULL | 规则描述 |
| from_status | int | NO | MUL | NULL | 源状态 |
| to_status | int | NO | MUL | NULL | 目标状态 |
| w_order_type | int | NO | MUL | NULL | 工单类型 |
| priority | int | NO |  | 0 | 优先级 |
| enabled | tinyint(1) | NO |  | 1 | 是否启用 |
| condition_expression | text | YES |  | NULL | 条件表达式 |
| required_permissions | text | YES |  | NULL | 必需权限 |
| required_role_ids | text | YES |  | NULL | 必需角色ID |
| require_approval | tinyint(1) | NO |  | 0 | 需要审批 |
| approval_process_key | varchar(100) | YES |  | NULL | 审批流程键 |
| pre_actions | text | YES |  | NULL | 前置动作 |
| post_actions | text | YES |  | NULL | 后置动作 |
| auto_transition_condition | text | YES |  | NULL | 自动转换条件 |
| auto_transition_delay | int | YES |  | NULL | 自动转换延迟 |
| ext_config | text | YES |  | NULL | 扩展配置 |
| sort | int | NO |  | 0 | 排序 |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 5. w_order_workflow_instance - 工单工作流实例表

记录工单工作流的执行实例。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| w_order_id | bigint | NO | MUL | NULL | 工单ID |
| template_id | bigint | NO | MUL | NULL | 模板ID |
| instance_code | varchar(50) | NO | UNI | NULL | 实例编码 |
| current_node_id | bigint | YES | MUL | NULL | 当前节点ID |
| workflow_status | int | NO | MUL | NULL | 工作流状态 |
| context_data | text | YES |  | NULL | 上下文数据 |
| variables | text | YES |  | NULL | 变量 |
| start_time | datetime | NO | MUL | CURRENT_TIMESTAMP | 开始时间 |
| end_time | datetime | YES |  | NULL | 结束时间 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |
| last_rollback_time | datetime | YES |  | NULL | 最后回滚时间 |
| rollback_count | int | NO |  | 0 | 回滚次数 |
| max_rollback_count | int | NO |  | 3 | 最大回滚次数 |

---

## 6. w_order_workflow_node - 工单工作流节点表

定义工作流模板中的节点配置。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| template_id | bigint | NO | MUL | NULL | 模板ID |
| node_code | varchar(50) | NO |  | NULL | 节点编码 |
| node_name | varchar(100) | NO |  | NULL | 节点名称 |
| node_type | int | NO |  | NULL | 节点类型 |
| parent_node_id | bigint | YES | MUL | NULL | 父节点ID |
| node_level | int | NO |  | 1 | 节点层级 |
| sort | int | NO |  | 0 | 排序 |
| auto_execute | tinyint(1) | NO |  | 0 | 自动执行 |
| timeout_hours | int | YES |  | NULL | 超时小时数 |
| required_roles | text | YES |  | NULL | 必需角色 |
| assignee_strategy | int | NO |  | 1 | 指派策略 |
| enter_condition | text | YES |  | NULL | 进入条件 |
| exit_condition | text | YES |  | NULL | 退出条件 |
| enter_actions | text | YES |  | NULL | 进入动作 |
| exit_actions | text | YES |  | NULL | 退出动作 |
| form_config | text | YES |  | NULL | 表单配置 |
| required_fields | text | YES |  | NULL | 必填字段 |
| description | text | YES |  | NULL | 描述 |
| enabled | tinyint(1) | NO |  | 1 | 是否启用 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 7. w_order_workflow_node_history - 工单工作流节点历史表

记录工作流节点的执行历史。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| instance_id | bigint | NO | MUL | NULL | 实例ID |
| node_id | bigint | NO | MUL | NULL | 节点ID |
| execution_id | varchar(50) | NO | MUL | NULL | 执行ID |
| node_status | int | NO | MUL | NULL | 节点状态 |
| assignee_id | bigint | YES | MUL | NULL | 指派人ID |
| assignee_name | varchar(100) | YES |  | NULL | 指派人姓名 |
| input_data | text | YES |  | NULL | 输入数据 |
| output_data | text | YES |  | NULL | 输出数据 |
| execution_result | text | YES |  | NULL | 执行结果 |
| error_message | text | YES |  | NULL | 错误信息 |
| start_time | datetime | NO | MUL | CURRENT_TIMESTAMP | 开始时间 |
| end_time | datetime | YES |  | NULL | 结束时间 |
| timeout_time | datetime | YES |  | NULL | 超时时间 |
| rollback_reason | text | YES |  | NULL | 回滚原因 |
| rollback_time | datetime | YES |  | NULL | 回滚时间 |
| rollback_by | bigint | YES |  | NULL | 回滚人 |
| rollback_to_node_id | bigint | YES | MUL | NULL | 回滚到节点ID |
| original_execution_id | varchar(50) | YES |  | NULL | 原始执行ID |
| comments | text | YES |  | NULL | 评论 |
| attachments | text | YES |  | NULL | 附件 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 8. w_order_workflow_node_rollback - 工单工作流节点回滚表

记录工作流节点的回滚操作。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| instance_id | bigint | NO | MUL | NULL | 实例ID |
| rollback_type | int | NO |  | 1 | 回滚类型 |
| from_node_id | bigint | NO | MUL | NULL | 源节点ID |
| to_node_id | bigint | NO | MUL | NULL | 目标节点ID |
| rollback_reason | text | NO |  | NULL | 回滚原因 |
| rollback_strategy | int | NO |  | 1 | 回滚策略 |
| rollback_by | bigint | NO | MUL | NULL | 回滚人 |
| rollback_by_name | varchar(100) | YES |  | NULL | 回滚人姓名 |
| rollback_time | datetime | NO | MUL | CURRENT_TIMESTAMP | 回滚时间 |
| rollback_status | int | NO | MUL | 1 | 回滚状态 |
| execution_start_time | datetime | YES |  | NULL | 执行开始时间 |
| execution_end_time | datetime | YES |  | NULL | 执行结束时间 |
| affected_node_ids | text | YES |  | NULL | 受影响节点ID |
| rollback_data | text | YES |  | NULL | 回滚数据 |
| rollback_variables | text | YES |  | NULL | 回滚变量 |
| auto_notify | tinyint(1) | NO |  | 1 | 自动通知 |
| auto_reassign | tinyint(1) | NO |  | 0 | 自动重新分配 |
| notification_config | text | YES |  | NULL | 通知配置 |
| require_approval | tinyint(1) | NO |  | 0 | 需要审批 |
| approval_status | int | YES |  | NULL | 审批状态 |
| approval_by | bigint | YES |  | NULL | 审批人 |
| approval_time | datetime | YES |  | NULL | 审批时间 |
| approval_comments | text | YES |  | NULL | 审批评论 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 9. w_order_workflow_template - 工单工作流模板表

定义工作流模板的基本信息。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| template_name | varchar(100) | NO |  | NULL | 模板名称 |
| w_order_type | int | NO | MUL | NULL | 工单类型 |
| template_code | varchar(50) | NO | MUL | NULL | 模板编码 |
| template_version | varchar(20) | NO |  | 1.0 | 模板版本 |
| description | text | YES |  | NULL | 描述 |
| is_default | tinyint(1) | NO |  | 0 | 是否默认 |
| enabled | tinyint(1) | NO |  | 1 | 是否启用 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |

---

## 10. w_order_workflow_transition - 工单工作流转换表

定义工作流节点之间的转换关系。

| 字段名 | 类型 | 是否为空 | 键 | 默认值 | 备注 |
|--------|------|----------|-----|--------|---------|
| id | bigint | NO | PRI | NULL | 主键，自增 |
| template_id | bigint | NO | MUL | NULL | 模板ID |
| from_node_id | bigint | NO | MUL | NULL | 源节点ID |
| to_node_id | bigint | NO | MUL | NULL | 目标节点ID |
| transition_name | varchar(100) | NO |  | NULL | 转换名称 |
| transition_code | varchar(50) | NO |  | NULL | 转换编码 |
| condition_expression | text | YES |  | NULL | 条件表达式 |
| required_roles | text | YES |  | NULL | 必需角色 |
| actions | text | YES |  | NULL | 动作 |
| auto_transition | tinyint(1) | NO |  | 0 | 自动转换 |
| sort | int | NO |  | 0 | 排序 |
| description | text | YES |  | NULL | 描述 |
| enabled | tinyint(1) | NO |  | 1 | 是否启用 |
| tenant_id | bigint | NO | MUL | 0 | 租户ID |
| creator | varchar(64) | NO |  |  | 创建人 |
| create_time | datetime | NO |  | CURRENT_TIMESTAMP | 创建时间 |
| updater | varchar(64) | NO |  |  | 更新人 |
| update_time | datetime | NO |  | CURRENT_TIMESTAMP | 更新时间 |
| deleted | bit(1) | NO |  | b'0' | 删除标记 |
| allow_rollback | tinyint(1) | NO |  | 1 | 允许回滚 |
| rollback_conditions | text | YES |  | NULL | 回滚条件 |
| rollback_actions | text | YES |  | NULL | 回滚动作 |

---

## 表关系说明

### 核心关系

1. **工单主表关系**：
   - `w_order` 是核心表，其他表通过 `w_order_id` 关联
   - `w_order_operation_record` 记录工单的操作历史
   - `w_order_workflow_instance` 记录工单的工作流执行实例

2. **模板关系**：
   - `w_order_template` 定义工单模板
   - `w_order_workflow_template` 定义工作流模板
   - `w_order_workflow_node` 定义工作流节点
   - `w_order_workflow_transition` 定义节点间转换

3. **工作流执行关系**：
   - `w_order_workflow_instance` 工作流实例
   - `w_order_workflow_node_history` 节点执行历史
   - `w_order_workflow_node_rollback` 节点回滚记录

4. **状态转换关系**：
   - `w_order_transition_rule` 定义状态转换规则
   - 与工单状态字段配合使用

### 索引说明

- 所有表都有基本的主键索引
- 外键字段都建立了索引以提高查询性能
- 状态、类型等常用查询字段建立了索引
- 时间字段建立了索引以支持时间范围查询

---

*文档生成时间：2024年*
*数据库：rrs_pro*