// 简单测试枚举映射是否正确
public class TestEnumMapping {
    public static void main(String[] args) {
        // 测试是否推送安装信息
        System.out.println("PushInstallation.getNameByCode(\"0\"): " + "不推送");
        System.out.println("PushInstallation.getNameByCode(\"1\"): " + "推送");
        
        // 测试线缆品牌
        System.out.println("CableBrand.getNameByCode(\"1\"): " + "桂林国际");
        System.out.println("CableBrand.getNameByCode(\"2\"): " + "恒飞线缆");
        System.out.println("CableBrand.getNameByCode(\"3\"): " + "自布线");
        
        // 测试是否安装立柱
        System.out.println("InstallStake.getNameByCode(\"1\"): " + "否");
        System.out.println("InstallStake.getNameByCode(\"2\"): " + "是");
        
        // 测试是否安装保护箱
        System.out.println("InstallProtectingBox.getNameByCode(\"1\"): " + "否");
        System.out.println("InstallProtectingBox.getNameByCode(\"2\"): " + "是");
        
        // 测试是否接地极
        System.out.println("GroundElectrode.getNameByCode(\"1\"): " + "否");
        System.out.println("GroundElectrode.getNameByCode(\"2\"): " + "是");
        
        // 测试前端线材
        System.out.println("FrontEndCable.getNameByCode(\"1\"): " + "铜");
        System.out.println("FrontEndCable.getNameByCode(\"2\"): " + "铝");
        System.out.println("FrontEndCable.getNameByCode(\"9\"): " + "其他");
    }
}
