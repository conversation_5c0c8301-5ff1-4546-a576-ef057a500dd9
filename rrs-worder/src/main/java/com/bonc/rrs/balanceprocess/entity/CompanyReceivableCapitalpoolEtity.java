package com.bonc.rrs.balanceprocess.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: 资金池表对象
 * @Author: liujunpeng
 * @Date: 2021/10/13 16:45
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@TableName("company_receivable_capitalpool")
@ApiModel(value = "资金池实体类")
public class CompanyReceivableCapitalpoolEtity implements Serializable {

    @TableId
    @ApiModelProperty(value = "ID")
    private Integer id;
    /**
     * 车企厂商
     */
    @ApiModelProperty(value = "车企厂商")
    private Integer companyId;
    /**
     * 凭证号
     */
    @ApiModelProperty(value = "凭证号")
    private String voucherNo;
    /**
     * 余额
     */
    @ApiModelProperty(value = "余额")
    private BigDecimal balance;
    /**
     * 可用余额
     */
    @ApiModelProperty(value = "可用余额")
    private BigDecimal availableBalance;
    /**
     * 0:正常，1:删除
     */
    @ApiModelProperty(value = "0:正常，1:删除")
    private Integer deleteState;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createOper;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateOper;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
