package com.bonc.rrs.balanceprocess.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 计算发票手动拆分金额
 * @Author: liujunpeng
 * @Date: 2021/12/10 11:47
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors
public class CalculatePriceQuery {

    @NotNull(message = "缺少费用类型!")
    @ApiModelProperty(value = "费用类型含税/不含税")
    private String priceType;

    @NotNull(message = "缺少含税总金额！")
    @ApiModelProperty(value = "含税总金额")
    private BigDecimal totalInvoiceFee;

    @NotNull(message = "缺少不含税总金额")
    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal totalNoTaxFee;

    @NotNull(message = "缺少税率")
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "数量")
    private Integer goodsNum;

    @ApiModelProperty(value = "下标")
    private Integer index;

    @NotNull
    @ApiModelProperty(value = "单张发票拆分信息")
    private List<InvoiceData> invoiceList;
}
