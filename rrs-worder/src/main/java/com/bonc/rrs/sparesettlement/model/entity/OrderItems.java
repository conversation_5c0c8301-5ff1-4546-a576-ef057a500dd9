package com.bonc.rrs.sparesettlement.model.entity;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 发票项目明细列表。每张发票最多一
 * 百条
 * <AUTHOR> 2020-3-24
 * */
@Data
public class OrderItems {
    private String code; //商品编码

    @NotEmpty(message = "商品名称必填")
    private String name;

//    private String spec; //规格型号

   // price quantity都为空或都不为空
    //商品单价。必须等于金额/数量的四舍五入值
//    private Number price;
//
//    //数量。必须大于等于 0.000001。
//    private Number quantity;

//    private String uom;

    //税率。只能为 0、0.03、0.04、0.06、0.11 或 0.17
    @NotEmpty(message = "税率必填")
    private Number taxRate;

    @NotEmpty(message = "税价合计金额必填")
    private Number amount;

//    //折扣金额，金额为正数
//    private Number discountAmount;
    //商品分类编码。目前的分类编码为 19 位
    @Size(min = 19,max = 19,message = "商品分类编码为19位,不足后面进行补0")
    private String catalogCode;
//    //当优惠政 策标识为 1 时必填
//    private String preferentialPolicyFlg; //增值税特殊管理。
//    //零税率标识。1:免税,2:不征税,3:普 通零税率。税率为零的情况下，如果不传则默认为 1。
//    private String zeroTaxRateFlg;
}
