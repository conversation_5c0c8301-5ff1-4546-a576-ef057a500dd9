package com.bonc.rrs.spider.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bonc.rrs.spider.dto.LxInstallOrderDto;
import com.bonc.rrs.worder.entity.WorderExtFieldEntity;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.validator.ValidatorUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class LxInstallOrderStrategy extends AbstractOrderStrategy {

    private static final Set<Integer> SUPPORTED_TEMPLATE_IDS = new HashSet<>(Arrays.asList(1, 2, 3));

    @Override
    public boolean supports(Integer templateId) {
        return SUPPORTED_TEMPLATE_IDS.contains(templateId);
    }

    @Override
    protected void validateStrategySpecificFields(Map<String, Object> extFields) {
//        extFields 转换成 LxInstallOrderDto
        //map转对象
        LxInstallOrderDto dto = BeanUtil.mapToBean(extFields, LxInstallOrderDto.class, true);

        // 专属字段校验示例
        if (ObjectUtil.isNull(extFields.get("installType"))) {
            throw new RRException("安装类型不能为空");
        }

        // 或使用Validator分组校验
        ValidatorUtils.validateEntity(dto, LxInstallOrderDto.InstallCheckGroup.class);
    }

    @Override
    protected void preProcess(OrderContext context) {
        LxInstallOrderDto apiDto = (LxInstallOrderDto) context.getRequestDto().getExtFields();
        List<WorderExtFieldEntity> extFieldList = context.getWorderInfoEntity().getWorderExtFieldList();
        extFieldList.add(WorderExtFieldEntity.create(102, "分中心", apiDto.getFzx()));
        extFieldList.add(WorderExtFieldEntity.create(1171, "客户ID号", apiDto.getCustId()));
        extFieldList.add(WorderExtFieldEntity.create(1170, "服务类型", apiDto.getServiceType()));
        extFieldList.add(WorderExtFieldEntity.create(1172, "创建人", apiDto.getCreateName()));
    }
}
