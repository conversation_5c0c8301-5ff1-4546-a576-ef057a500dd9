package com.bonc.rrs.baidumap.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bonc.rrs.baidumap.entity.BaiduGridBasisEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.baidumap.vo.AreaDotAttendant;
import com.bonc.rrs.baidumap.vo.BaiduMapSearchDataVo;
import com.bonc.rrs.baidumap.vo.GridListDataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 百度网格区域基础表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@Mapper
public interface BaiduGridBasisDao extends BaseMapper<BaiduGridBasisEntity> {

    /**
     * 根据品牌查询关联的区域集合
     *
     * @param brandId
     * @return
     */
    List<Integer> selectAreaIdByBrand(@Param("brandId") Integer brandId);

    /**
     * 根据区域查询服务经理
     *
     * @param areaIds
     * @return
     */
    List<Map<String,String>> selectSerivceByArea(@Param("areaIds") List<Integer> areaIds,@Param("param") GridListDataVo param);

    /**
     * 根据网点查询服务兵数量
     *
     * @param dotIds
     * @return
     */
    List<Map<String, Integer>> selectAttendantByDot(@Param("dotIds") Set<Integer> dotIds);

    /**
     * 根据前端条件和用户关联的权限区域查询网格
     *
     * @param baiduMapSearchDataVo
     * @param areaIds
     * @return
     */
    List<BaiduGridBasisEntity> selectGridByserviceManager(@Param("query") BaiduMapSearchDataVo baiduMapSearchDataVo, @Param("areaIds") String areaIds);

    /**
     * 根据品牌获取服务经理名称获取关联的区域ID
     *
     * @param serviceManager
     * @param brandId
     * @return
     */
    List<Integer> selectAreaIdByBrandAndManager(@Param("serviceManager") String serviceManager, @Param("brandId") Integer brandId);

    /**
     * 根据网格级别查询所有关联区域的服务经理
     *
     * @return
     */
    List<Map<String, Object>> selectManagerAndAreaIdAll(@Param("param") BaiduMapSearchDataVo baiduMapSearchDataVo);

    /**
     * 把网点和品牌关联的所有区域查询
     *
     * @param dotId
     * @param brandId
     * @return
     */
    List<Integer> selectAreaIdByDotOrBrand(@Param("dotId") Integer dotId, @Param("brandId") Integer brandId);

    /**
     * 查询网点关联表根据网点和区域分组，获取区域，网点，服务兵数量
     *
     * @return
     */
    List<AreaDotAttendant> selectAreaDotAttendant(@Param("param") BaiduMapSearchDataVo param);

    /**
     * 分页查询网格数据
     *
     * @param page
     * @param params
     * @return
     */
    IPage<BaiduGridBasisEntity> selectGridPage(Page<BaiduGridBasisEntity> page, @Param("query") GridListDataVo params, @Param("regionIds") List<Long> regionIds, @Param("areaIds") String areaIds, @Param("userAreaIds") String userAreaIds);

    /**
     * 查询区域对应的品牌数据
     *
     * @param userIds
     * @return
     */
    Set<String> selectBrandByArea(@Param("regcode") String regcode,@Param("userIds") Set<Integer> userIds);

    /**
     * 根据用户ID查询当前绑定的区域集合
     *
     * @param userId
     * @return
     */
    List<Integer> selectAreaIdByUserId(@Param("userId") Long userId);
}
