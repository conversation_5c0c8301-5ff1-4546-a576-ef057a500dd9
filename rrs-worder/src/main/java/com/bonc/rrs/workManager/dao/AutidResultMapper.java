package com.bonc.rrs.workManager.dao;


import com.bonc.rrs.workManager.entity.AutidResult;
import org.apache.ibatis.annotations.Param;

public interface AutidResultMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(AutidResult record);

    int insertSelective(AutidResult record);

    AutidResult selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AutidResult record);

    int updateByPrimaryKey(AutidResult record);

    int getCountAutidResult(@Param(value = "worderNo") String worderNo,
                            @Param(value = "purpose") Integer purpose);

    int updataWorderInformation(@Param(value = "worderNo") String worderNo,
                                @Param(value = "status") Integer status,
                                @Param(value = "statusValue") String statusValue);

    int updateWorderTime(@Param(value = "worderNo")String worderNo);

    Integer selectAutidType(@Param(value = "worderNo") String worderNo,
                            @Param(value = "purpose") Integer purpose);

    Integer updateWorderMajorState(@Param(value = "worderNo") String worderNo,
                                   @Param(value = "majorState") Integer majorState,
                                   @Param(value = "record") String record);

    /**
     * 更新客服确认安装完成时间
     * @param worderNo
     */
    void updateConfirmTime(@Param(value = "worderNo") String worderNo);
}