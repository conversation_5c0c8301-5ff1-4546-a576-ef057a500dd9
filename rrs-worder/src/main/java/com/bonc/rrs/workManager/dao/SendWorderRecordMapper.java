package com.bonc.rrs.workManager.dao;

import com.bonc.rrs.workManager.entity.SendWorderRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 派单记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-09
 */
public interface SendWorderRecordMapper extends BaseMapper<SendWorderRecord> {

    /**
     *  根据网点和区域查询周期内所有派单记录总和
     * @param dotIdList
     * @param regcode
     * @return
     */
    List<Map<String,Object>> selectCycleDotSendCount(@Param("dotIdList") Set<Integer> dotIdList, @Param("regcode") String regcode,@Param("brandId") Integer brandId, @Param("cycleTime") Date cycleTime);

    List<SendWorderRecord> selectCycleDotSendList(@Param("dotIdList") Set<Integer> dotIdList,@Param("areaId") Integer areaId,@Param("brandId") Integer brandId,@Param("cycleTime") Date cycleTime,@Param("operationType") Integer operationType);

    List<SendWorderRecord> selectCycleDotSendListByArea(@Param("dotIdList") Set<Integer> dotIdList,@Param("areaId") Integer areaId,@Param("brandId") Integer brandId,@Param("cycleTime") Date cycleTime,@Param("operationType") Integer operationType);

}
