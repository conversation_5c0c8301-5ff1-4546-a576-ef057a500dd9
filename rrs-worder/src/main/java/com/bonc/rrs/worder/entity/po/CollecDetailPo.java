package com.bonc.rrs.worder.entity.po;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 收费明细
 */
@Data
public class CollecDetailPo implements Serializable {
    /**支付单号*/
    private String payCode;

    /**用户增项结算总金额(含税)*/
    private BigDecimal userBalanceFeeSum;

    /**网点给客户的优惠金额*/
    private BigDecimal dotIncreDisCountAmount;

    /**实际金额 */
    private BigDecimal userActualCost;

}
