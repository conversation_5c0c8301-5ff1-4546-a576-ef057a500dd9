package com.bonc.rrs.worder.common;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.domain.PushCheckOrder;
import com.bonc.rrs.byd.domain.PushCheckOrderPicAttachment;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.worder.dao.WorderIntfMessageDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.BizRegionService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 * @description 检查工单入库定时任务
 * @date 2025/07/21 17:00
 */
@Slf4j
@Component("messageSaveCheckOrderTask")
public class MessageSaveCheckOrderTask implements ITask {

    @Autowired
    private WorderIntfMessageDao worderIntfMessageDao;
    @Autowired
    private BrandService brandService;
    @Autowired
    private BizRegionService bizRegionService;
    @Autowired
    private WorderTemplateService worderTemplateService;
    @Autowired
    private WorderInformationService worderInformationService;

    private void createDefaultLoginUser() {
        DefaultWebSecurityManager defaultWebSecurityManager = new DefaultWebSecurityManager();
        SecurityUtils.setSecurityManager(defaultWebSecurityManager);

        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }

    @Override
    @Lock4j(expire = 330000)
    public void run(String params) {
        log.info("检查工单入库定时任务 ---- 开始");
        // 查询检查工单消息，bid=8
        List<WorderIntfMessageEntity> worderIntfMessageEntityList = worderIntfMessageDao.queryNotSaveOrder(8);

        if (worderIntfMessageEntityList != null) {
            createDefaultLoginUser();
            for (WorderIntfMessageEntity worderIntfMessageEntity : worderIntfMessageEntityList) {
                log.info("message save check order {} start.", worderIntfMessageEntity.getOrderCode());
                try {
                    PushCheckOrder pushCheckOrder = JSON.parseObject(worderIntfMessageEntity.getData(), PushCheckOrder.class);
                    saveCheckOrder(worderIntfMessageEntity.getId(), pushCheckOrder);
                } catch (Exception e) {
                    updateMessageTypeFail(worderIntfMessageEntity.getId(), e.toString());
                }

                log.info("message save check order {} end", worderIntfMessageEntity.getOrderCode());
            }
        }
        log.info("检查工单入库定时任务 ---- 结束");
    }

    public void updateWorderId(Integer messageId, Integer worderId) {
        worderIntfMessageDao.updateWorderIdById(messageId, worderId);
    }

    public void updateMessageTypeFail(Integer messageId, String errorMsg) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 2, errorMsg);
        SmsUtil.sendSms("15910305046", "推送比亚迪检查订单入库失败", "【到每家科技服务】");
    }

    public void updateMessageTypeFail(Integer messageId, String errorMsg, String orderCode) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 2, errorMsg);
        SmsUtil.sendSms("15910305046", "推送比亚迪检查订单入库失败,订单号:" + orderCode, "【到每家科技服务】");
    }

    public void updateMessageTypeRepeatWorder(Integer messageId, String errorMsg) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 1, errorMsg);
    }

    private void saveCheckOrder(Integer messageId, PushCheckOrder pushCheckOrder) {
        R r = null;
        try {
            BrandEntity brandEntity = brandService.queryBrandByCompanyBrandId(pushCheckOrder.getCarBrand());
            if (brandEntity == null) {
                log.info("message save check order " + pushCheckOrder.getOrderCode() + " 非法品牌");
                updateMessageTypeFail(messageId, "非法品牌");
                return;
            }

            RegionCode regionCode = convertRegion(pushCheckOrder.getProvinceCode(), pushCheckOrder.getCityCode(), pushCheckOrder.getAreaCode());

            if (regionCode.getProvinceCode() == null || regionCode.getCityCode() == null || regionCode.getAreaCode() == null) {
                log.info("message save check order " + pushCheckOrder.getOrderCode() + " 地区不匹配");
                updateMessageTypeFail(messageId, "地区不匹配");
                return;
            }

            // 查询检查工单模板，工单类型为8（SERVE_CHECK）brand = 22 比亚迪
            List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(
                    22, 8, regionCode.getProvinceCode().intValue(), regionCode.getCityCode().intValue());
            if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
                log.info("message save check order " + pushCheckOrder.getOrderCode() + " 没有对应的检查工单模板");
                updateMessageTypeFail(messageId, "没有对应的检查工单模板");
                return;
            }
            worderTemplateDtoList.sort(Comparator.comparing(WorderTemplateDto::getTemplateId).reversed());
            WorderTemplateDto worderTemplateDto = worderTemplateDtoList.get(0);

            if (validateCompanyOrderNumberAndBrandExsit(pushCheckOrder.getOrderCode(), worderTemplateDto.getTemplateId())) {
                log.info("message save check order " + pushCheckOrder.getOrderCode() + " 车企订单号已存在，无法创建工单");
                updateMessageTypeRepeatWorder(messageId, "车企订单号已存在，无法创建工单");
                return;
            }

            // 匹配表情符
            String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

            // 比亚迪公司ID
            Integer companyId = 516;

            WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

            String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + pushCheckOrder.getAddress();
            address = address.replaceAll(regex, "");

            String userName = pushCheckOrder.getContactName();
            userName = userName.replaceAll(regex, "");

            String dispatchTime = "";
            if (pushCheckOrder.getDispatchTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dispatchTime = sdf.format(pushCheckOrder.getDispatchTime());
            }

            String installationCompletedTime = "";
            if (pushCheckOrder.getInstallationCompletedTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                installationCompletedTime = sdf.format(pushCheckOrder.getInstallationCompletedTime());
            }

            worderInfoEntity.setPushOrderWorderSource("byd");
            worderInfoEntity.setUserName(userName);
            worderInfoEntity.setUserPhone(pushCheckOrder.getContactMobile());
            worderInfoEntity.setAddress(address);
            worderInfoEntity.setCompanyOrderNumber(pushCheckOrder.getOrderCode());
            worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

            worderInfoEntity.setCarBrand(brandEntity.getId() + "");
            worderInfoEntity.setCarModel("4");
            worderInfoEntity.setCompanyId(companyId);

            worderInfoEntity.setPostcode("");
            worderInfoEntity.setWorderSourceTypeValue("");
            // 检查工单类型
            worderInfoEntity.setWorderTypeId(8);

            worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
            worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

            List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

            // 基础工单信息
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", worderInfoEntity.getWorderTypeId()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", companyId));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", dispatchTime));

            // 检查工单特有字段
            worderExtFieldEntityList.add(setWorderExtFieldEntity(305, "备注-创建", "检查工单"));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", ""));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", userName));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderInfoEntity.getUserPhone()));

            // 检查工单相关字段
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", pushCheckOrder.getWallboxName()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", pushCheckOrder.getWallboxPower()));

            // 检查工单特有字段
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3001, "子订单编号", pushCheckOrder.getSubOrderCode()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3002, "任务序号", pushCheckOrder.getTaskSerial()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3003, "充电桩编码", pushCheckOrder.getWallboxCode()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3004, "质保截止日期", pushCheckOrder.getWarrantyEnd()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3005, "安装订单号", pushCheckOrder.getInstallOrderCode()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3006, "是否推送安装信息", pushCheckOrder.getPushInstallation()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3007, "安装完成时间", installationCompletedTime));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3008, "物料编码", pushCheckOrder.getWallboxMaterialCode()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3009, "取电方式", pushCheckOrder.getPowerSupplyMethod()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3010, "线缆品牌", pushCheckOrder.getCableBrand()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3011, "线缆规格", pushCheckOrder.getCableType()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3012, "线缆长度", pushCheckOrder.getCableLength()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3013, "断路器品牌", pushCheckOrder.getBreakerBrand()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3014, "断路器型号", pushCheckOrder.getBreakerType()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3015, "是否安装立柱", pushCheckOrder.getInstallStake()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3016, "是否安装保护箱", pushCheckOrder.getInstallProtectingBox()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3017, "是否接地极", pushCheckOrder.getGroundElectrode()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(3018, "前端线材", pushCheckOrder.getFrontEndCable()));
            // 将图片附件字符串转换为对象
            PushCheckOrderPicAttachment picAttachment = JSON.parseObject(pushCheckOrder.getPicAttrs(), PushCheckOrderPicAttachment.class);
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2167, "施工使用线缆", picAttachment.getConstructionImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2168, "充电桩序列码", picAttachment.getSequenceImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2169, "用线始端", picAttachment.getLineStartImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2170, "用线末端", picAttachment.getLineEndImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2171, "接地线或接地极", picAttachment.getGroundWireImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2172, "电源点火零电压", picAttachment.getZeroVoltageImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2173, "人桩合照", picAttachment.getManPileImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2174, "安装确认单", picAttachment.getConfirmationImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2175, "增项收费单", picAttachment.getIncreaseChargeImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2176, "漏保上端火零绝缘电阻", picAttachment.getFireZeroResistanceImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2177, "漏保下端零地电压", picAttachment.getZeroGroundVoltageImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2178, "试充照片", picAttachment.getTrialChargeImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2179, "充电桩铭牌图片", picAttachment.getPileNameplateImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2080, "放弃电力报装免责声明", picAttachment.getDisclaimersImage()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2181, "同级负载确认书", picAttachment.getLoadConfirmationImage()));

            worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

            r = saveWorderInformation(worderInfoEntity);
        } catch (Exception e) {
            log.error("message save check order " + pushCheckOrder.getOrderCode() + " 出现异常", e);
            updateMessageTypeFail(messageId, "推送检查订单基本信息给指定供应商接口下单失败，" + e.toString(), pushCheckOrder.getOrderCode());
            return;
        }

        if (!IntegerEnum.ZERO.getValue().equals(r.get(WarningConstant.CODE))) {
            log.info("message save check order " + pushCheckOrder.getOrderCode() + r.get("msg"));
            updateMessageTypeFail(messageId, r.get("msg") + "");
            return;
        }
        String worderNo = r.get("worderNo") + "";
        WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(worderNo);

        updateWorderId(messageId, worderInformationEntity.getWorderId());

        // 自动派单
        goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME);
    }

    /**
     * 转换比亚迪区域编码
     */
    private RegionCode convertRegion(String provinceCode, String cityCode, String areaCode) {
        RegionCode regionCode = new RegionCode();

        if (StringUtils.isNotBlank(provinceCode)) {
            Long bydProvinceCode = Long.valueOf(provinceCode);
            BizRegionEntity provinceRegion = bizRegionService.getRegionByBydCode(bydProvinceCode);
            if (provinceRegion != null) {
                regionCode.setProvinceCode(provinceRegion.getId());
            }
        }

        if (StringUtils.isNotBlank(cityCode)) {
            Long bydCityCode = Long.valueOf(cityCode);
            BizRegionEntity cityRegion = bizRegionService.getRegionByBydCode(bydCityCode);
            if (cityRegion != null) {
                regionCode.setCityCode(cityRegion.getId());
            } else {
                regionCode.setCityCode(regionCode.getProvinceCode());
            }
        } else {
            regionCode.setCityCode(regionCode.getProvinceCode());
        }

        if (StringUtils.isNotBlank(areaCode)) {
            Long bydAreaCode = Long.valueOf(areaCode);
            BizRegionEntity areaRegion = bizRegionService.getRegionByBydCode(bydAreaCode);
            if (areaRegion != null) {
                regionCode.setAreaCode(areaRegion.getId());
            } else {
                regionCode.setAreaCode(regionCode.getCityCode());
            }
        } else {
            regionCode.setAreaCode(regionCode.getCityCode());
        }
        return regionCode;
    }

    private Boolean validateCompanyOrderNumberAndBrandExsit(String companyOrderNumber, Integer templateId) {
        return worderInformationService.validateCompanyOrderNumberAndBrandExsit(companyOrderNumber, templateId);
    }

    private WorderExtFieldEntity setWorderExtFieldEntity(Integer fieldId, String fieldName, Object fieldValue) {
        String value = "";
        if (fieldValue != null) {
            value = String.valueOf(fieldValue);
        }
        return WorderExtFieldEntity.builder()
                .fieldId(fieldId)
                .fieldName(fieldName)
                .fieldValue(value).build();
    }

    public R saveWorderInformation(WorderInfoEntity worderInfoEntity) {
        log.info("保存检查工单信息 {}", JSON.toJSONString(worderInfoEntity));
        return worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
    }

    private WorderInformationEntity getWorderInformationByWorderNo(String worderNo) {
        QueryWrapper<WorderInformationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("worder_no", worderNo);
        return worderInformationService.getOne(queryWrapper);
    }

    private Integer goAutoSendWorder(String worderNo, String operatorName) {
        try {
            Results results = worderInformationService.goAutoSendWorder(worderNo, operatorName, null);
            // 修改工单状态
            worderInformationService.updateWorderStatus(worderNo);
            return results.getDotId();
        } catch (Exception e) {
            log.error("检查工单自动派单异常: {}", e.getMessage());
            return null;
        }
    }

}
