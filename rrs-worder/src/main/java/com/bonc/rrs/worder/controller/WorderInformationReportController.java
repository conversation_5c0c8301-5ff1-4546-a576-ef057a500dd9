package com.bonc.rrs.worder.controller;


import com.alibaba.fastjson.JSONObject;
import com.bonc.rrs.baidumap.annotations.LogPrint;
import com.bonc.rrs.worder.service.WorderInformationReportService;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 工单固化表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@RestController
@RequestMapping("/worder/report")
@RequiredArgsConstructor
@Api(description = "工单固化表 前端控制器")
public class WorderInformationReportController {

    final WorderInformationReportService worderInformationReportService;

    @RequestMapping(value = "/getExtField")
    @LogPrint("工单扩展属性查询接口")
    @ApiOperation("工单扩展属性查询接口")
    public R getExtField(@RequestBody JSONObject param) {
        if(param == null || !param.containsKey("worderNo")){
            return R.error("缺少必填参数!");
        }
        return worderInformationReportService.getExtFields(param.getString("worderNo"));
    }

}

