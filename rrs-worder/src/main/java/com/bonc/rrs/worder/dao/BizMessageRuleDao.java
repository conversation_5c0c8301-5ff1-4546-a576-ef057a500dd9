package com.bonc.rrs.worder.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.worder.entity.BizMessageRuleEntity;
import com.bonc.rrs.worder.entity.dto.BizMessageRuleDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 消息预警信息表
 *
 * <AUTHOR>
 * @date 2019-02-26
 */
@Mapper
public interface BizMessageRuleDao extends BaseMapper<BizMessageRuleEntity> {

    /**
     * 根据参数查找一条记录
     * @param  bizMessageRuleDto
     * @return BizMessageRuleEntity
     */
    BizMessageRuleEntity queryOneByDto(BizMessageRuleDto bizMessageRuleDto);
}
