/**
 * Copyright (C), 2024,
 */
package com.bonc.rrs.supervise.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.supervise.entity.SuperviseDictionary;
import com.bonc.rrs.supervise.entity.SuperviseDictionaryTripType;
import org.apache.ibatis.annotations.Mapper;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/22 15:22
 * @Version 1.0.0
 */
@Mapper
public interface SuperviseDictionaryTripTypeMapper extends BaseMapper<SuperviseDictionaryTripType> {
}