package com.bonc.rrs.supervise.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bonc.rrs.supervise.entity.SuperviseRecoverRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 督办工单回复记录表;(supervise_recover_record)表数据库访问层
 * <AUTHOR> liujunpeng
 * @date : 2023-9-26
 */
@Mapper
public interface SuperviseRecoverRecordMapper  extends BaseMapper<SuperviseRecoverRecord>{
     List<SuperviseRecoverRecord> selectRecoverList(@Param("teleIds") List<Integer> teleIds);
 }