package bonc.storage.modules.systemmanagement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by liqing<PERSON><PERSON> on 2020/1/6.
 * 用户基础信息表 实体类
 */
@Data
@TableName("user")
public class UserEntity implements Serializable {
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;
    private String avatar;
    private String account;
    private String password;
    private String salt;
    private String name;
    private Date birthday;
    private String sex;
    private String email;
    private String phone;
    @TableField("roleid")
    private String roleId;
    private Integer status;
    @TableField("createtime")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
    @TableField(exist = false)
    private List<StoreEntity> storeList;
}
