package bonc.storage.modules.storemanager.service;

import bonc.storage.core.utils.PageUtils;
import bonc.storage.core.utils.Query;
import bonc.storage.core.utils.Result;
import bonc.storage.modules.storemanager.entity.RRSAlertManagerEntity;
import com.alibaba.fastjson.JSONObject;

/**
 * 商品基础信息
 */
public interface IRRSAlertManagerService {

    PageUtils alertManagerList(Query query);

    PageUtils goodsAlertList(Query query);

    Result deleteAlertManager(RRSAlertManagerEntity managerEntity);

    Result updateGoodsAlertDown(JSONObject param);

    Result batchOperation(JSONObject param);

    boolean sendSmsAlert(String mobile,String content) throws Exception;

    Integer getAlertNumById(Integer storeId, String materielId);

    Result insertMateriel(JSONObject para);
}