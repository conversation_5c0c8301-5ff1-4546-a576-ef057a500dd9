package bonc.storage.modules.storemanager.service;

import bonc.storage.modules.storemanager.entity.StoreInventoryDetailsEntity;
import bonc.storage.modules.storemanager.vo.CheckSnVo;
import bonc.storage.modules.storemanager.vo.StoreInventoryDetailsExcelProperty;
import bonc.storage.modules.storemanager.vo.StoreInventoryDetailsVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.youngking.lenmoncore.common.utils.R;

import java.util.List;

/**
 * <p>
 * 盘库详情表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-21
 */
public interface StoreInventoryDetailsService extends IService<StoreInventoryDetailsEntity> {

    /**
     * 盘库详情列表接口
     *
     * @param params
     * @return
     */
    R queryListByInventoryRecordId(StoreInventoryDetailsVo params);

    /**
     * SN校验接口
     *
     * @param params
     * @return
     */
    R checkSn(CheckSnVo params);

    /**
     * 详情下载查询
     * @param params
     * @return
     */
    List<StoreInventoryDetailsExcelProperty> downLoadQuery(StoreInventoryDetailsVo params);

    /**
     * 确认盘点接口
     *
     * @param inventoryRecordId
     * @return
     */
    R submitInventory(Integer inventoryRecordId);

    /**
     * 确认盘点前校验和统计接口
     *
     * @param inventoryRecordId
     * @return
     */
    R preInventoryCheck(Integer inventoryRecordId);

    /**
     * 更改num
     * @param entity
     * @return
     */
    R updateNumber(StoreInventoryDetailsEntity entity);

    /**
     * 更改SN号
     * @param entity
     * @return
     */
    R updateSnNumber(StoreInventoryDetailsEntity entity);

    /**
     * 比较差异
     * @param params
     * @return
     */
    R verifySnTotal(StoreInventoryDetailsEntity params);

    /**
     * 获取字典表物料名称
     * @return
     */
    R getBrandName();
}
