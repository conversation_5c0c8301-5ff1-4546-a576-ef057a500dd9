package bonc.storage.modules.storemanager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ASUS on 2020/4/7.
 */
@TableName("rrs_enter_leave_mode")
@Data
public class RRSEnterLeaveModeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public interface Delete {
    }

    public interface Update {
    }

    public interface Check {
    }

    @NotNull(message = "id不能为空",groups = Delete.class)
    private int id;

    @NotEmpty(message = "name不能为空",groups = {Update.class,Check.class})
    private String name;

    //出库或者入库或者调拨
    @NotEmpty(message = "mode不能为空",groups = Update.class)
    private String mode;

    private Date createTime;

    private Date updateTime;
}
