# Tomcat
server:
    tomcat:
        uri-encoding: UTF-8
        max-threads: 1000
        min-spare-threads: 30
    port: 8080
    connection-timeout: 5000
    servlet:
      context-path: /smt

spring:
    # 环境 dev|test|pro
    profiles:
        active: test
    # jackson时间格式化
    jackson:
        time-zone: GMT+8
        date-format: yyyy-MM-dd HH:mm:ss
    servlet:
        multipart:
            max-file-size: 100MB
            max-request-size: 100MB
            enabled: true
    resources: # 指定静态资源的路径
        static-locations: classpath:/static/,classpath:/views/
    redis:
        open: false  # 是否开启redis缓存  true开启   false关闭
        database: 0
        host: localhost
        port: 16379
        password:       # 密码（默认为空）
        timeout: 6000  # 连接超时时长（毫秒）
        jedis:
            pool:
                max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
                max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-idle: 10      # 连接池中的最大空闲连接
                min-idle: 5       # 连接池中的最小空闲连接

mybatis-plus:
    mapper-locations: classpath*:mapper/**/*.xml
    #实体扫描，多个package用逗号或者分号分隔
    typeAliasesPackage: bonc.storage.modules.*.entity
    global-config:
        #数据库相关配置
        db-config:
            #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
            id-type: AUTO
            #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
            field-strategy: NOT_NULL
            #驼峰下划线转换
            column-underline: true
            logic-delete-value: -1
            logic-not-delete-value: 0
        banner: false
    #原生配置
    configuration:
        map-underscore-to-camel-case: true
        cache-enabled: false
        call-setters-on-nulls: true
        #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
    level.root: warn
    level.bonc.storage: debug
    path: logs/
    file: storage.log

scheduler:
    group: rrs_storage