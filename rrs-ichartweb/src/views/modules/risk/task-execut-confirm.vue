<template>
  <div class="risk-task-execut-confirm">
    <div class="pa-title">
      <h2>任务信息</h2>
    </div>
    <task-info :task="task"></task-info>

    <div class="pa-title"><h2>问卷列表</h2></div>
    <el-tabs class="pa20" v-model="edTabName" type="card">
      <el-tab-pane
        v-for="(item,index) in tabList"
        :key="item.sheetEnName"
        :label="item.sheetName"
        :name="item.sheetEnName"
        @click="tabClick()">
        <span slot="label" style="padding: 8px">
         {{item.sheetName}}
          <!--<span style="margin-left: 10px;"><i class="el-icon-edit" @click="addOrUpdateTab(item.id)"></i></span>-->
        </span>
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="评价范围" name="1" v-if="!item.relOption || item.relOption === '[]'">
            <!--<basic-item :sid="item.id" :companyId="companyId" :task="task"></basic-item>-->
            <base-item :sid="item.id" :task="taskItem" @showThisAraeEvent="showThisAraeEvent" :handleName="handleName"
                       @getTemplateIds="getTemplateIds"></base-item>
            <!--<base-item ref="basicItem" :sid="item.id" :tid="task.tempId"  :view="view"></base-item>-->
          </el-collapse-item>
          <el-collapse-item title="评价维度" name="3">
            <fill-item :sid="item.id" :tid="task.tempId" :view="view"></fill-item>
          </el-collapse-item>
        </el-collapse>
      </el-tab-pane>
    </el-tabs>

    <el-form rel="form" label-width="80px" class="pa20" v-if="!handleName">
      <div class="text-center"  v-if="isAuth('operational:taskexecution:confirmandback')">
        <el-form-item label="退回原因:" v-show=" taskItem.taskStatus===3 ">
          <el-input type="textarea" v-model="form.content"></el-input>
        </el-form-item>
        <el-form-item class="text-center">
          <el-button type="primary" @click="reject()" v-if="taskItem.taskStatus===3 ">退回</el-button>
          <el-button type="primary" @click="commit()" v-if="taskItem.taskStatus===3 || taskItem.taskStatus==7">确认
          </el-button>
          <!--<el-button type="primary" @click="goback()">关闭</el-button>-->
        </el-form-item>
      </div>
      <div class="text-center">
<!--        <el-form  label-width="80px" >-->
<!--          <el-form-item label="分发说明:" v-if="taskItem.taskStatus===5">-->
<!--            <el-input type="textarea" v-model="form.content"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
        <!--<el-button type="primary" @click="toGL()">保存</el-button>-->
        <!--<el-button type="primary" @click="commitTemp()" v-if="isFinish">确认模板</el-button>-->
        <!--<el-button type="primary" v-if="task.taskStatus==4" disabled="disabled">模板已确认</el-button>-->
        <el-button type="primary" @click="resendLC()" v-if="taskItem.taskStatus===5">修改后重新下发</el-button>
        <!--<el-button type="primary" @click="goback()">关闭</el-button>-->
      </div>

    </el-form>



    <audit-record :taskItem="taskItem" :ruid="taskItem.receiveUserId" :bizType="bizType"></audit-record>

  </div>
</template>
<script>
  import basicItem from './task-list-check-base-item';
  import taskInfo from './task-list-task-info';
  import fillItem from './template/fill-item';
  // import baseItem from './template/basic-item';
  import baseItem from './task-execut-confirm-base-item';
  import auditRecord from './task-execut-audit-record';
  import {Loading} from 'element-ui';
  //bizType 1.流程审批  2.数据审批  3.任务回退
  export default {
    components: {
      basicItem, taskInfo, fillItem, baseItem, auditRecord
    },
    data() {
      return {
        isShowThisArae: true,
        view: true,
        bizType: '3',
        activeName: ['1'],
        edTabName: 'second',
        tabList: [],
        task: {},
        taskItem: {},
        taskDept: {},
        companyId: '',
        auditList: [],
        // 判断角色
        isHaveRole: false,
        operateRle: false,
        form: {},
        selectBids: [],
        handleName: undefined
      }
    },
    created: function () {
      /*this.task = this.$route.params.task;
      this.taskItem = this.$route.params.task;
      this.companyId = this.task.companyId; // 对应公司ID

      if (this.taskItem.taskStatus === 3) {
        this.bizType = 3
      }
      if (this.taskItem.taskStatus === 7) {
        this.bizType = 1
      }*/
      // this.initAuditList();
      //   this.getTaskStatusInfo();
      //this.queryTaskInfo(this.taskItem.id)
      this.handleName = this.$route.params.handleName;
      let task_ = this.$route.params.task;
      if (task_ && this.taskItem.id !== task_.id) {
        this.$destroy();
      }
      if (task_) {
        this.task = this.$route.params.task;
        this.taskItem = this.$route.params.task;
        this.companyId = this.task.companyId; // 对应公司ID

        if (this.taskItem.taskStatus === 3) {
          this.bizType = 3
        }
        if (this.taskItem.taskStatus === 7) {
          this.bizType = 1
        }
        this.queryTaskInfo(this.taskItem.id)
      }
      // this.pdUserRole()
    },
    methods: {
      pdUserRole() {
        this.$http({
          url: this.$http.adornUrl(`/system/user/getRoleList/${this.$store.state.user.id}`),
          method: 'get',
          params: this.$http.adornParams()
        }).then(({data}) => {
          if (data && data.code === 0) {
            console.log(data.roleList)
            data.roleList.forEach((item) => {
              if (item === 5 || item === 4 || item === 17 || item === 18) {
                this.isHaveRole = true
              }
              if (item === 1 || item === 4 || item === 3 || item === 16 || item === 17) {
                this.operateRle = true
              }
            })
          }
        })
      },
      showThisAraeEvent(isShow) {
        this.isShowThisArae = isShow;
      },
      queryTaskInfo(taskId) {
        let me = this;
        // 查询任务信息 evaluatetask
        this.$http({
          url: this.$http.adornUrl('/operational/evaluatetask/getTaskAndTempInfo'),
          method: 'get',
          params: this.$http.adornParams({taskId: taskId})
        }).then(({data}) => {
          me.task = data && data.code === 0 ? data.task : [];
          // this.log('getTaskAndTempInfo=', me.task);
          // 查询sheet列表
          this.getTabList(me.task.tempId);
        });
      },
      getTabList(tempId) {
        let me = this;
        this.$http({
          url: this.$http.adornUrl('/operational/templatesheet/select'),
          method: 'get',
          params: this.$http.adornParams({tid: tempId})
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tabList = data.data;
            me.edTabName = this.tabList[0].sheetEnName;
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      resendLC() {
        // let status = 3; // 待确认/已下发
        // if (this.taskItem.tempType === 1 && this.taskItem.distributionType === 1) {
        //   status = 13; // 直接分发设置为6 重新填写.
        // }
        this.$confirm('确认是否重新下发?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/operational/basic/resendBasic'),
            method: 'get',
            params: this.$http.adornParams({
              taskId: this.taskItem.id,
              tempId: this.taskItem.tempId,
              deptId: this.taskItem.deptId,
              bizType: this.bizType,
              astatus: 3,
              ruid: this.taskItem.receiveUserId,
              suid: this.taskItem.sendUserId,
              dkbmUid: this.taskItem.dkbmUid,
              content: this.form.content,
              btyType: 'cxxf'
            })
            // params: {params:this.currRow}
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              this.alert('操作成功！');
              //this.taskItem.taskStatus = 3
              this.$changeRouter('risk-task-execut')
              this.closeThisTab()
            } else {
              this.alert('操作失败，请重试。');
            }
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      reject() {
        if (this.selectBids.length === 0) {
          this.$message.warning('请选择流程!');
          return;
        }
        console.log(this.selectBids, 'tttttt');
        let ruid_ = this.taskItem.receiveUserId;
        if (this.taskItem.tempType === 1) {  // 通用模板 this.tempType = 1
          ruid_ = this.taskItem.sendUserId;
        }
        this.$confirm('确认是否退回?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/operational/basic/rejectQuestionBasic'),
            method: 'get',
            params: this.$http.adornParams({
              taskId: this.taskItem.id,
              tempId: this.taskItem.tempId,
              deptId: this.taskItem.deptId,
              bizType: this.bizType,
              astatus: 5,
              ruid: ruid_,
              suid: this.taskItem.sendUserId,
              dkbmUid: this.taskItem.dkbmUid,
              dkUid: this.taskItem.dkUserId,
              content: this.form.content,
              basicIds: this.selectBids.join(',')
            })
            // params: {params:this.currRow}
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.alert('操作成功！');
              loadingInstance.close();
              // this.taskItem.taskStatus = 5
              this.closeThisTab()
            } else {
              loadingInstance.close();
              this.alert('操作失败，请重试。');
            }
          });
        }).catch(() => {
          this.alert('取消!');
        });
      },
      commit() {
        let astatus = 4; // 已确认/待流程审核
        if (this.taskItem.tempType === 1 && this.taskItem.distributionType === 1) {
          astatus = 6; // 待填写
        }
        this.$confirm('是否确定完成?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/operational/basic/basicCommit'),
            method: 'get',
            params: this.$http.adornParams({
              taskId: this.taskItem.id,
              tempId: this.taskItem.tempId,
              ruid: this.taskItem.receiveUserId,
              suid: this.taskItem.sendUserId,
              dkbmUid: this.taskItem.dkbmUid,
              bizType: this.bizType,
              astatus: astatus,
              ruidIsNull: 1,
              content: ''
            })
            // params: {params:this.currRow}
          }).then(({data}) => {
            loadingInstance.close()
            if (data && data.code === 0) {
              this.alert('操作成功！');
              // this.taskItem.taskStatus = 4
              this.$changeRouter('risk-task-execut')
              this.closeThisTab()
            } else {
              if (data.ruidIsNullSize) {
                this.alert('错误：存在 ' + data.ruidIsNullSize + ' 项任务未指派！');
              } else if (data.invalidItemNum) {
                this.alert('错误：存在 ' + data.invalidItemNum + ' 项任务数据填写不完整，请补全后提交！');
              } else {
                this.alert('操作失败，请重试。');
              }
            }
          });
        }).catch(() => {
          this.alert('取消!');
        });
      },
      goback(row) {
        this.$emit('closeCurrentTab')
      },
      // ----------------------------关闭页签
      closeThisTab() {
        let com = this;
        let i = 0;
        while (!com.tabsCloseCurrentHandle && i < 20) {
          com = com.$parent;
          i++;
        }
        console.log('parent level = ', i); // router-view -> 4
        com.tabsCloseCurrentHandle();
        // this.$emit('closeCurrentTab')
      },

      initAuditList: function () {
        this.$http({
          url: this.$http.adornUrl('/operational/taskauditrecord/list'),
          method: 'get',
          params: this.$http.adornParams({taskId: this.task.id, qbasicIdIsNull: 1, orderBy: 'create_time desc'})
        }).then(({data}) => {
          this.log('upastatus  =', data);
          if (data && data.code === 0) {
            this.auditList = data.datas;
          } else {
            this.$message.error(data.msg);
          }
        });
      },
      getTemplateIds(selectBids) {
        this.selectBids = selectBids;
      }
    }
  }
</script>
