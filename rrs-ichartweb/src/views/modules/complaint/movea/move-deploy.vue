<template>
  <div class="mod-config">
    <el-row>
      <el-form :inline='true' :model="formInline" class='demo-form-inline' @keyup.enter.native="getQueryList()" label-width="90px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="配置名称">
              <el-input v-model="formInline.roleName" placeholder="配置名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label='机构'>
              <common-async-mgtree-ts @validChange="changeOrgId" :singdeptName="formInline.comDeptName"
                                      ref="deptSelTreeCom" :mgr="1">
              </common-async-mgtree-ts>
            </el-form-item>
          </el-col>
        </el-row>
        <!--<el-form-item label='问题类型'>
          <selector dictKey="COMPLAIN_TYPE" v-model="formInline.complaintType" clearable></selector>
        </el-form-item>-->
        <div class="text-right" style="margin-bottom: 20px">
          <el-form-item>
            <el-button type="primary" @click="addClick()">新增</el-button>
            <el-button type="primary" @click="pageIndex = 1;getQueryList()">查询</el-button>
            <el-button type="warning" @click="clearData()">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-row>
    <el-row class="row-bg row-query-separator"> </el-row>
    <el-row>
      <el-table
        border
        class="pa20"
        size="small"
        :data="dataList"
        v-loading="dataListLoading"
        :header-cell-style="{background:'#f5f4f4',color:'#333',fontWeight:'bold',textAlign:'center'}"
        style="width: 100%;text-align: center;">
        <el-table-column
          type="index"
          label="序号"
          width="80">
        </el-table-column>
        <el-table-column
          prop="roleName"
          label="配置名称"
          width="150">
        </el-table-column>
        <el-table-column
          prop="comDeptId"
          label="机构"
          width="250"
          :formatter="formatterComDeptId">
        </el-table-column>
        <el-table-column
          prop="cirModel"
          label="流转模式"
          width="100"
          :formatter="formatterCirModel">
        </el-table-column>
        <el-table-column
          prop="cirWay"
          label="流转方式"
          width="100"
          :formatter="formatterCirWay">
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建日期"
          width="150">
        </el-table-column>
        <el-table-column
          prop="status"
          label="状态"
          :formatter="formatterStatus">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" v-if="(scope.row.status === 0 || scope.row.status === 2)" @click="employRoleStart(scope.row,1)">启用
            </el-button>
            <el-button type="text" size="small" v-if="(scope.row.status === 0 || scope.row.status === 2)" @click="employEdit(scope.row)">编辑
            </el-button>
            <el-button type="text" size="small" v-if="(scope.row.status === 0 || scope.row.status === 2)" @click="employDelete(scope.row)">删除
            </el-button>
            <el-button type="text" size="small" v-if="scope.row.status === 1" @click="employRoleStop(scope.row,2)">停用
            </el-button>
            <el-button type="text" size="small" @click="employSee(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
        :current-page="pageIndex"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="totalPage"
        layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
    </el-row>
    <!--弹窗  查看-->
  </div>
</template>
<script>
  import { Loading } from 'element-ui';
  import selector from '@/components/dict/selector'
  import commonAsyncMgtreeTs from '@/components/dept/common-async-mgtree-ts'

  export default {
    data() {
      return {
        seeInfoVisible: false,
        addOrUpdateVisible: false,
        pageIndex: 1,
        pageSize: 10,
        totalPage: 0,
        dataListLoading: false,
        // 查询条件
        formInline: {
          roleName: '',
          complaintType: undefined,
          comDeptId: undefined, // 机构ID
          comDeptName: ''       // 机构名称
        },
        // 分页数据
        dataList: [],
        userId: this.$store.state.user.id,
        // 专业公司
        companyList: []
      }
    },
    components: {
      selector,
      commonAsyncMgtreeTs
    },
    created () {
      let query = this.$route.query;
      this.pageIndex = (query.page && Number(query.page)) || this.pageIndex;
      this.pageSize = (query.limit && Number(query.limit)) || this.pageSize;
      this.formInline.roleName = query.roleName || this.formInline.roleName;
      //this.formInline.complaintType = query.complaintType || this.formInline.complaintType;
      this.formInline.comDeptId = (query.comDeptId && Number(query.comDeptId)) || this.formInline.comDeptId;
      this.getQueryList(1)
    },
    // 页面初始化
    activated() {
      this.getQueryList()
    },
    methods: {
      // ---------------------------------------------------------------页面初始化 start
      //获取专业公司
      initOrgList() {
        let ids = [];
        this.dataList.forEach(item => {
          ids.push(item.comDeptId);
        })
        if (ids.length > 0) {
          this.$http({
            url: this.$http.adornUrl('/sys/dept/getDeptList'),
            method: 'post',
            data: this.$http.adornData({
              'deptIds': ids
            })
          }).then(({data}) => {
            this.companyList = data && data.code === 0 ? data.list : [];
          })
        }
      },
      // 获取数据列表
      getQueryList(isRefresh) {
        let query = {
          'page': this.pageIndex,
          'limit': this.pageSize,
          'roleName': this.formInline.roleName,
          'createPersonId': this.userId,
          'deployType': 1,
          //'complaintType': this.formInline.complaintType,
          'comDeptId': this.formInline.comDeptId
        };
        if (!isRefresh) {
          this.$router.push({query: query});
          let nameQuery = {name: this.$route.name, query: query};
          this.$store.commit('common/updateMainTabsQuery', nameQuery);
        }
        this.dataListLoading = true
        this.$http({
          url: this.$http.adornUrl('/complainTwo/tsmovedeploy/queryPage'),
          method: 'get',
          params: this.$http.adornParams(query)
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.dataList = data.page.list
            this.totalPage = data.page.totalCount
            this.initOrgList()
          } else {
            this.dataList = []
            this.totalPage = 0
          }
          this.dataListLoading = false
        })
      },
      // -----------------------------------------------------------------------------初始化 end
      // --------------------------------------------------------------------------------------------数据过滤 start
      // 过滤状态
      formatterStatus(row, column) {
        return this.$store.getters['bizcache/getDictItemByKeyAndId']('TS_EMPLOY_STATUS', parseInt(row.status));
      },
      // 过滤专业公司
      formatterComDeptId(row, column) {
        const res = this.companyList.find(i => parseInt(i.deptId) === row.comDeptId);
        return res ? res.deptName : '';
        //return this.$store.getters['bizcache/getDeptNameById'](row.comDeptId);
      },
      // 过滤流转模式
      formatterCirModel(row, column) {
        return this.$store.getters['bizcache/getDictItemByKeyAndId']('TS_CIR_MODEL', parseInt(row.cirModel));
      },
      // 过滤流转方式
      formatterCirWay(row, column) {
        return this.$store.getters['bizcache/getDictItemByKeyAndId']('TS_CIR_WAY', parseInt(row.cirWay));
      },
      // --------------------------------------------------------------------------------------------数据过滤 end
      // ----------------------------------------------新增
      addClick: function () {
        localStorage.removeItem('params:moveDeployAddOrUpdate');
        this.$router.push({name: 'moveDeployAddOrUpdate'});
      },
      // ----------------------------------------------启用
      employRoleStart: function (item, status) {
        this.$confirm('确认是否启用?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          // 判断流转方式
          if (item.cirWay === 2) {
            // 自动流转必须有流转链条----2019-03-27
            if (item.cirModel === 1) {
              // 统一模式
              this.$http({
                url: this.$http.adornUrl('/complainTwo/tsprocesschain/isHaveChain'),
                method: 'get',
                params: this.$http.adornParams({
                  busiId: item.businessId,
                  zdMoveType: -1,
                  processType: 2,
                  orderBy: 'order_id,id'
                })
              }).then(({data}) => {
                if (data && data.code === 0) {
                  if (data.datas === true) {
                    // 启用规则前，先判断当前专业公司是否有已经启用的配置，如果有则不能启用。
                    this.commStart(loadingInstance, status, item)
                  } else {
                    loadingInstance.close()
                    this.alert('流转方式为自动流转，则必须设置流转链条！');
                  }
                }
              }).catch(() => {
                loadingInstance.close()
              });
            } else {
              // 按问题类型
              this.$http({
                url: this.$http.adornUrl('/complainTwo/tsprocesschain/isHaveChain'),
                method: 'get',
                params: this.$http.adornParams({
                  busiId: item.businessId,
                  complaintType: 'COMPLAIN_TYPE',
                  processType: 2,
                  orderBy: 'order_id,id'
                })
              }).then(({data}) => {
                if (data && data.code === 0) {
                  console.log(data.datas)
                  if (data.datas === true) {
                    // 启用规则前，先判断当前专业公司是否有已经启用的配置，如果有则不能启用。
                    this.commStart(loadingInstance, status, item)
                  } else {
                    loadingInstance.close()
                    this.alert('流转方式为自动流转，则必须设置流转链条！');
                  }
                }
              }).catch(() => {
                loadingInstance.close()
              });
            }
          } else {
            // 人工流转
            this.commStart(loadingInstance, status, item)
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      commStart(loadingInstance, status, item) {
        // 启用规则前，先判断当前专业公司是否有已经启用的配置，如果有则不能启用。
        this.$http({
          url: this.$http.adornUrl('/complainTwo/tsmovedeploy/selectStartData'),
          method: 'post',
          params: this.$http.adornParams({
            'comDeptId': item.comDeptId,
            'deployType': 1,
            'status': status
          })
        }).then(({data}) => {
          if (data && data.isOnly === true) {
            loadingInstance.close()
            this.alert('专业公司同时只可启用一个配置！');
          } else {
            // 如果没找到，则启用
            this.$http({
              url: this.$http.adornUrl('/complainTwo/tsmovedeploy/updateStatus'),
              method: 'post',
              params: this.$http.adornParams({
                'id': item.id,
                'status': status
              })
            }).then(({data}) => {
              if (data && data.code === 0) {
                this.$message({
                  type: 'success',
                  message: '启用成功!'
                });
              } else {
                this.$message({
                  type: 'false',
                  message: '启用失败!'
                });
              }
              this.getQueryList() // 刷新列表
              loadingInstance.close()
            }).catch(() => {
              loadingInstance.close()
            })
          }
        }).catch(() => {
          loadingInstance.close()
        });
      },
      // ----------------------------------------------停用
      employRoleStop: function (item, status) {
        this.$confirm('确认是否停用', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complainTwo/tsmovedeploy/updateStatus'),
            method: 'post',
            params: this.$http.adornParams({
              'id': item.id,
              'status': status
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '停用成功!'
              });
            } else {
              this.$message({
                type: 'false',
                message: '停用失败!'
              });
            }
            this.getQueryList() // 刷新列表
            loadingInstance.close()
          }).catch(() => {
            loadingInstance.close()
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      // ----------------------------------------------删除
      employDelete: function (item) {
        this.$confirm('确认是否删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let loadingInstance = Loading.service({
            lock: true,
            text: 'Loading',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          this.$http({
            url: this.$http.adornUrl('/complainTwo/tsmovedeploy/deleteStatus'),
            method: 'post',
            params: this.$http.adornParams({
              'id': item.id
            })
          }).then(({data}) => {
            if (data && data.code === 0) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              });
            } else {
              this.$message({
                type: 'false',
                message: '删除失败!'
              });
            }
            this.getQueryList() // 刷新列表
            loadingInstance.close()
          }).catch(() => {
            loadingInstance.close()
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '取消'
          });
        });
      },
      // ----------------------------------------------编辑
      employEdit: function (item) {
        this.$router.push({name: 'moveDeployAddOrUpdate', params: {task: item}});
      },
      // ----------------------------------------------查看
      employSee: function (item) {
        this.$router.push({name: 'moveDeployView', params: {task: item}})
      },
      // ------------------------------------------------------分页
      // 每页数
      sizeChangeHandle(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.getQueryList()
      },
      // 当前页
      currentChangeHandle(val) {
        this.pageIndex = val
        this.getQueryList()
      },
      // --------------------------------------------------重置
      clearData() {
        this.formInline = {}
        this.pageIndex = 1;
        this.getQueryList();
      },
      changeOrgId (val) {
        console.log(val.deptName, val.deptId);
        this.formInline.comDeptId = val.deptId;
        this.formInline.comDeptName = val.deptName;
      }
    }
  }
</script>
<style scoped>
  .last-title {
    padding: 10px 8px;
  }
  .el-tooltip__popper {
    max-width: 400px;
    line-height: 130%;
  }
  .tsed-row {
    color: #FF0000;
  }

  .mod-config >>> .el-form-item__content {
    width: 60%;
    height: 40px;
  }

  .mod-config >>> .el-form-item {
    margin-bottom: 10px;
    width: 100%;
  }
</style>
