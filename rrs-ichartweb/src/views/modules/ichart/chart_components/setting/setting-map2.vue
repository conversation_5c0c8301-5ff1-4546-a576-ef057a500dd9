<template>
  <div class="chart-card" style="width:100%;height: 100%;padding: 5px 10px; border:0px solid #e0e0e0">
    <el-tabs v-model="activeName">
      <el-tab-pane label="数据设置" name="data">
        <el-form label-width="80px">
          <son-chart ref="sonPie" :tableTitle="tableTitle" :params="params" :currObj="currObj"
                     @screenField="screenField" @setDataName="setDataName"
                     :layout="layout"></son-chart>
          <!--系列名称加值-->
          <div>
            <setting-font :font="font" :set-font="setFont"/>
            <el-row class="data-name-title">
              <label>系列</label>
            </el-row>
            <!--            <el-row class="data-name-con">-->
            <!--              <label>名称</label>-->
            <!--              <el-input size="mini" v-model="currObj._option.series[0].name" style="width: 53%"></el-input>-->
            <!--            </el-row>-->
            <el-row class="data-name-con">
              <label>省份列</label>
              <el-select style="width: 68%" size="mini" v-model="nameSelect" placeholder="请选择列名称" @change="onChange1">
                <el-option v-for="item in tableTitle"
                           :key="item.id"
                           :label="item.displayName"
                           :value="item.fieldName"></el-option>
              </el-select>
            </el-row>
            <el-row class="data-name-con">
              <label>数值列</label>
              <el-select style="width: 68%" size="mini" v-model="valueSelect" placeholder="请选择列名称" @change="onChange2">
                <el-option v-for="item in tableTitle"
                           :key="item.id"
                           :label="item.displayName"
                           :value="item.fieldName"></el-option>
              </el-select>
            </el-row>
            <el-row class="refresh" style="text-align: right;padding:5px 0px;margin:10px 15px">
                <span type="info" style="color: #15A193;margin-left: 55%;cursor: pointer" @click="extract()">
                  刷新
                </span>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="样式设置" name="style">
        <son-map1-SetUp @exeParentRefresh="exeParentRefresh" :exe-parent-refresh="exeParentRefresh" :params="params" :currObj="currObj"></son-map1-SetUp>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
  import sonChart from './subclass/son-chart';
  import SonMap1SetUp from './subclass/son-map1-SetUp';
  import SettingFont from './setting-font'
  // 记录当前card下的所有的组件引用
  export default {
    props: {
      params: Object,
      currObj: Object,
      layout: Array
    },
    data() {
      return {
        activeName: 'data',
        dataSelect: '',
        nameSelect: '',
        valueSelect: '',
        tableTitle: [],
        font: {
          color: '#333',
          fontSize: 12,
          fontStyle: 'normal'
        }
      }
    },
    components: {
      sonChart: sonChart,
      SonMap1SetUp: SonMap1SetUp,
      SettingFont: SettingFont
    },
    methods: {
      setDataName(name) {
        this.dataSelect = name;
        this.nameSelect = '';
        this.valueSelect = '';
      },
      //找到父级对应的方法进行修改图表
      exeParentRefresh() {
        let com = this;
        let i = 0;
        while (!com._upQueryCurrObj && i < 20) {
          com = com.$parent;
          i++;
        }
        com._upQueryCurrObj(this.currObj);
      },
      //数据筛选列名列表渲染
      screenField(data) {
        this.$http({
          // url: this.$http.adornUrl('/warning/warningdatasource/uptateBygetItem'),
          url: this.$http.adornUrl('/ichart/dataSourceItem/getDataSourceByNameAndCode'),
          method: 'get',
          params: this.$http.adornParams({
            'tableCode': data,
            'type': "1"
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.tableTitle = data.data;
          }
        })
      },
      //列名称
      onChange1(val) {
        this.nameSelect = val
      },
      //值
      onChange2(val) {
        this.valueSelect = val
      },
      setFont(font) {
        this.font = font;
      },
      //刷新
      extract() {
        if (this.dataSelect === '') {
          this.$message('请选择数据名称');
        } else if (this.nameSelect === '') {
          this.$message('请选择列名称');
        } else if (this.valueSelect === '') {
          this.$message('请选择值名称');
        } else {
          this.currObj._option.dsSetting.nameColName = this.nameSelect
          this.currObj._option.dsSetting.valueColName = this.valueSelect
          this.currObj.option["textStyle"] = this.font;
          if (!this.currObj.option.dataRange.hasOwnProperty('textStyle')) {
            this.currObj.option.dataRange.textStyle = {}
          }
          this.currObj.option.dataRange["textStyle"] = this.font;
          this.currObj.option.series.map(item => {
            if (this.font.fontSize !== "") {
              item.itemStyle.normal.label["fontSize"] = this.font.fontSize;
            } else {
              delete item.itemStyle.normal.label.fontSize;
            }
            item.itemStyle.normal.label["color"] = this.font.color;
            item.itemStyle.normal.label["fontStyle"] = this.font.fontStyle;
            return item;
          })
          this.exeParentRefresh()
        }
      },
      //回显
      EchoDisplay() {
        this.dataSelect = this.currObj._option.tableName;
        this.nameSelect = this.currObj._option.dsSetting.nameColName
        this.valueSelect = this.currObj._option.dsSetting.valueColName
      }
    },
    created() {
      this.font = {...this.font, ...this.currObj.option.series[0].itemStyle.normal.label}
    },
    mounted() {
      this.EchoDisplay()
    }
  }
</script>
<style>
  .chart-card .el-tabs__item {
    width: 65%;
    text-align: center
  }

  .filter-1 .el-select {
    width: 100%;
  }

  .data-name {
    padding: 5px 10px;
    border-bottom: 1px solid #eee;
    line-height: 30px
  }

  .data-name-title {
    padding: 5px 0px;
    border-bottom: 1px solid #eee;
    color: #000;
    font-size: 16px
  }

  .data-name-con {
    margin: 3% 5%
  }

  .data-name-con label {
    display: inline-block;
    width: 20%
  }
</style>
