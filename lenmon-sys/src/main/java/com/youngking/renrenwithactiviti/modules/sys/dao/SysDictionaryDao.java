package com.youngking.renrenwithactiviti.modules.sys.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.youngking.renrenwithactiviti.modules.sys.entity.SysDictionaryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 字典记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-05 09:29:43
 */
@Mapper
public interface SysDictionaryDao extends BaseMapper<SysDictionaryEntity> {
	SysDictionaryEntity findOneByDicNumber(String dicNumber);

    String selectDictionaryByNumber(@Param("number") String number);
}
