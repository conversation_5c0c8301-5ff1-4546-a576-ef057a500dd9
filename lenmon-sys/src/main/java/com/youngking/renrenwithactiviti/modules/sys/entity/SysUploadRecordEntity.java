package com.youngking.renrenwithactiviti.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件上传记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-05-05 09:29:43
 */
@Data
@TableName("sys_upload_record")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysUploadRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private Integer id;
	/**
	 * 附件相对地址 /img/666.png
	 */
	private String fileUrl;
	/**
	 * 附件地址前缀 http://www.baidu.com
	 */
	private String fileFirsturl;
	/**
	 * 原附件名称
	 */
	private String fileName;
	/**
	 * 文件类型
	 */
	private String fileType;
	/**
	 * 文件大小
	 */
	private Long fileSize;
	/**
	 * 
	 */
	private Integer createUserId;
	/**
	 * 
	 */
	private Date createTime;
	/**
	 * 
	 */
	private Integer updateUserId;
	/**
	 * 
	 */
	private Date updateTime;
	/*
	* uuid
	* */
	private String uuid;
	/*
	* 文件目录
	* */
	private String businessDir;
	/*
	* 状态
	* */
	private Integer status;

}
